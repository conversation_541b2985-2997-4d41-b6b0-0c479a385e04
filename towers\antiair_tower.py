from .tower import Tower
import pygame
import math


class AntiAirTower(Tower):
    """Tower specialized for targeting flying enemies"""

    def __init__(self, x, y):
        super().__init__(x, y, 'antiair')
        self.damage = 18
        self.range = 175
        self.fire_rate = 48  # Slightly faster firing
        self.projectile_speed = 5.0
        self.size = 12
        self.color = (0, 191, 255)  # Deep sky blue

        # Anti-air targeting capabilities
        self.can_target_flying = True
        self.can_target_invisible = False
        self.can_target_ground = True
        self.prioritize_flying = True

        # Finalize initialization to update base stats
        self.finalize_initialization()

    def acquire_target(self, enemies):
        """Find target, prioritizing flying enemies"""
        flying_targets = []
        ground_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                if hasattr(enemy, 'flying') and enemy.flying:
                    flying_targets.append((enemy, distance))
                else:
                    ground_targets.append((enemy, distance))

        # Prioritize flying enemies
        if flying_targets:
            flying_targets.sort(key=lambda x: x[1])
            self.target = flying_targets[0][0]
        elif ground_targets:
            # Can hit ground enemies too, but with lower priority
            ground_targets.sort(key=lambda x: x[1])
            self.target = ground_targets[0][0]
        else:
            self.target = None

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def shoot(self, projectiles):
        """Fire homing missile at targets"""
        if self.target:
            try:
                from projectiles import HomingProjectile

                missile = HomingProjectile(
                    self.x, self.y, self.target.x, self.target.y,
                    self.projectile_speed, self.damage, self.tower_type
                )
                missile.target_enemy = self.target

                # Link projectile to tower for damage tracking
                missile.source_tower_id = self.tower_id
                projectiles.append(missile)

                # Generate currency immediately when firing
                self.generate_firing_currency()
            except Exception:
                # Fallback to basic projectile
                from projectiles import BasicProjectile
                projectile = BasicProjectile(
                    self.x, self.y, self.target.x, self.target.y,
                    self.projectile_speed, self.damage, self.tower_type
                )
                projectile.source_tower_id = self.tower_id
                projectiles.append(projectile)
                self.generate_firing_currency()

    # Removed custom draw method - using base Tower class draw method instead
    # This eliminates any anti-air specific drawing code that might cause the "little square" issue

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        flying_targets = []
        ground_targets = []

        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                if hasattr(enemy, 'flying') and enemy.flying:
                    flying_targets.append((enemy, actual_distance))
                else:
                    ground_targets.append((enemy, actual_distance))

        # Prioritize flying enemies
        if flying_targets:
            flying_targets.sort(key=lambda x: x[1])
            self.target = flying_targets[0][0]
        elif ground_targets:
            ground_targets.sort(key=lambda x: x[1])
            self.target = ground_targets[0][0]
        else:
            self.target = None

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier
