#!/usr/bin/env python3
"""
Test script to generate a game configuration and demonstrate AI logging functionality.
This will create a log file showing all AI inputs and outputs.
"""

import os
import sys
import json
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_logging():
    """Test the AI logging functionality by generating a configuration"""
    
    print("🧪 Testing AI Logging Functionality")
    print("=" * 50)
    
    try:
        # Import the adaptive config generator
        from ai.adaptive_config_generator import AdaptiveConfigGenerator
        
        # Create the generator with modular AI enabled
        print("🔧 Initializing Adaptive Config Generator...")
        generator = AdaptiveConfigGenerator(use_modular_ai=True)
        
        if not generator.ai_available:
            print("❌ AI is not available. Please check your API key configuration.")
            return
        
        print("✅ AI is available and ready!")
        
        # Create some sample performance data to trigger AI generation
        sample_performance = {
            'score': 65.0,
            'win_flag': True,
            'previous_difficulty': 50,
            'tower_diversity': 4,
            'config_file_path': 'config/tower_defense_game.json',
            'multi_game_context': {
                'win_rate': 60.0,
                'avg_score': 62.5,
                'trend': 'improving'
            }
        }
        
        print("🎮 Generating configuration with sample performance data...")
        print(f"   Score: {sample_performance['score']}%")
        print(f"   Win: {sample_performance['win_flag']}")
        print(f"   Previous Difficulty: {sample_performance['previous_difficulty']}")
        
        # Generate the configuration
        config = generator.generate_config_from_recent_games(performance_data=sample_performance)
        
        if config:
            level_name = config.get('level_name', 'Generated Level')
            print(f"✅ Configuration generated successfully: {level_name}")
            
            # Check if AI log was created
            ai_logs_dir = "ai_logs"
            if os.path.exists(ai_logs_dir):
                log_files = [f for f in os.listdir(ai_logs_dir) if f.startswith('ai_generation_log_')]
                if log_files:
                    latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(ai_logs_dir, x)))
                    log_path = os.path.join(ai_logs_dir, latest_log)
                    
                    print(f"📝 AI interaction log created: {log_path}")
                    
                    # Load and display summary of the log
                    try:
                        with open(log_path, 'r', encoding='utf-8') as f:
                            log_data = json.load(f)
                        
                        print(f"📊 Log Summary:")
                        print(f"   Total AI calls: {log_data.get('total_ai_calls', 0)}")
                        print(f"   Generation session: {log_data.get('generation_session', 'unknown')}")
                        
                        # Show function breakdown
                        interactions = log_data.get('interactions', [])
                        function_counts = {}
                        for interaction in interactions:
                            func_name = interaction.get('function', 'unknown')
                            function_counts[func_name] = function_counts.get(func_name, 0) + 1
                        
                        print(f"   AI functions called:")
                        for func_name, count in function_counts.items():
                            print(f"     - {func_name}: {count} times")
                        
                        # Show sample of first interaction
                        if interactions:
                            first_interaction = interactions[0]
                            print(f"\n📋 Sample AI Interaction (first call):")
                            print(f"   Function: {first_interaction.get('function', 'unknown')}")
                            print(f"   Timestamp: {first_interaction.get('timestamp', 'unknown')}")
                            
                            input_data = first_interaction.get('input', {})
                            system_msg = input_data.get('system_message', '')
                            user_prompt = input_data.get('user_prompt', '')
                            
                            print(f"   System Message: {system_msg[:100]}...")
                            print(f"   User Prompt Length: {len(user_prompt)} characters")
                            
                            output_data = first_interaction.get('output', {})
                            ai_response = output_data.get('raw_ai_response', '')
                            parsed_result = output_data.get('parsed_result')
                            error = output_data.get('error')
                            
                            print(f"   AI Response Length: {len(ai_response)} characters")
                            print(f"   Parsed Successfully: {'Yes' if parsed_result else 'No'}")
                            if error:
                                print(f"   Error: {error}")
                        
                        print(f"\n📁 Full log available at: {log_path}")
                        
                    except Exception as e:
                        print(f"❌ Error reading log file: {e}")
                else:
                    print("❌ No AI log files found")
            else:
                print("❌ AI logs directory not found")
        else:
            print("❌ Configuration generation failed")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project root directory")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_logging()
