# AI Level Generation - Complete Inputs and Outputs Documentation

This document contains all the inputs sent to AI and the expected outputs when generating new levels in the tower defense game using the **Modular AI Generation System**.

## MODULAR AI GENERATION SYSTEM

### System Message (Input)
```
You are an expert tower defense game designer. Generate complete, valid JSON configurations. Output only JSON, no explanatory text.
```

### User Prompt Template (Input)
The full AI generation system sends a comprehensive prompt that includes:

#### Performance Analysis Section
```
PLAYER PERFORMANCE ANALYSIS:
Score: {score}/100
Victory: {'Yes' if win_flag else 'No'}
Lives Remaining: {lives_remaining}/{starting_lives}
Tower Diversity: {tower_diversity} different types used
Previous Config Difficulty: {previous_difficulty}
```

#### Previous Configuration Information
```
PREVIOUS LEVEL CONFIGURATION:
{previous_config_info}
```

#### Terrain Mechanics and Rules
```
TERRAIN MECHANICS AND STRATEGIC USAGE:
- 0=Grass: Standard placement, all towers allowed, no bonuses/penalties
- 1=Path: Enemy route (normal speed), no towers can be placed here
- 2=Rock: Impassable obstacles, no towers, creates choke points
- 3=Water: ONLY Freezer/Splash towers allowed, +50% freeze effect duration
- 4=Forest: All towers allowed, -20% range but +30% damage (high-damage focus)
- 5=Sand: Enemy route (50% FASTER speed), use for difficult levels instead of normal path

CRITICAL TERRAIN PLACEMENT RULES:
- For EASY/MEDIUM levels: Use PATH (value 1) for normal enemy speed
- For DIFFICULT levels: Use SAND (value 5) for entire path to make all enemies 50% faster
- WATER/FOREST/GRASS: Place around the path for tower placement with different restrictions
```

#### Strategic Terrain Placement Rules
```
MANDATORY STRATEGIC TERRAIN PLACEMENT RULES:
YOU MUST FOLLOW THESE EXACT PERCENTAGES BASED ON PLAYER WEAKNESSES:

If Tower Diversity = 0-2 types (FORCE EXPERIMENTATION):
→ Place EXACTLY 35-40% Water terrain (terrain value 3) AROUND the path for tower placement
→ Place EXACTLY 25-30% Forest terrain (terrain value 4) AROUND the path for tower placement
→ Place ONLY 20-25% Grass terrain (terrain value 0) AROUND the path
→ Use Rock obstacles (terrain value 2) for 10-15% to create chokepoints
→ Use normal PATH (1) for standard difficulty

If Lives Remaining < 50% of starting lives (NEEDS EASIER CHALLENGE):
→ Place 30-35% Water terrain (terrain value 3) AROUND the path for freeze tower placement
→ Use Rock placement (terrain value 2) to create defensive chokepoints
→ Use normal PATH (1) for standard enemy speed to help them learn

If Score >= 80% (HIGH SKILL - DIFFICULT CHALLENGE):
→ Use SAND (terrain value 5) for the ENTIRE enemy path to make all enemies 50% faster
→ Mix 20-25% each of Water and Forest terrain AROUND the path
→ Create a challenging level with fast enemies requiring advanced strategies
```

#### Enemy Health and Speed Modification Rules
```
ENEMY HEALTH MODIFIER RULES:
- Score 0-30% AND Lost: enemy_buffs.max_health_modifier = 0.7 (reduce enemy health 30%)
- Score 31-50% AND Lost: enemy_buffs.max_health_modifier = 0.85 (reduce enemy health 15%)
- Score 51-70% AND Won: enemy_buffs.max_health_modifier = 1.0 (keep same health)
- Score 71-85% AND Won: enemy_buffs.max_health_modifier = 1.2 (increase enemy health 20%)
- Score 86-100% AND Won: enemy_buffs.max_health_modifier = 1.4 (increase enemy health 40%)

ENEMY SPEED MODIFIER RULES:
- Score 0-30% AND Lost: enemy_buffs.max_speed_modifier = 0.8 (reduce enemy speed 20%)
- Score 31-50% AND Lost: enemy_buffs.max_speed_modifier = 0.9 (reduce enemy speed 10%)
- Score 51-70% AND Won: enemy_buffs.max_speed_modifier = 1.0 (keep same speed)
- Score 71-85% AND Won: enemy_buffs.max_speed_modifier = 1.15 (increase enemy speed 15%)
- Score 86-100% AND Won: enemy_buffs.max_speed_modifier = 1.3 (increase enemy speed 30%)
```

#### Required JSON Output Structure
```
YOU MUST OUTPUT COMPLETE VALID JSON WITH ALL REQUIRED SECTIONS. The JSON must include:

REQUIRED TOP-LEVEL SECTIONS (CRITICAL - DO NOT OMIT):
{
  "level_name": "Creative Level Name",
  "game_config": {
    "starting_lives": 20,
    "starting_money": 500,
    "lives_per_wave": 0
  },
  "map_config": {
    "default_map": {
      "width": {width},
      "height": {height},
      "terrain": [[terrain grid with strategic placement]],
      "path": [[x,y] coordinates for enemy path]
    }
  },
  "wave_config": {
    "total_waves": {total_waves},
    "spawn_config": {"wave patterns with strategic enemies"},
    "enemy_buffs": {
      "max_health_modifier": [apply health modifier based on performance rules above],
      "max_speed_modifier": [apply speed modifier based on performance rules above]
    }
  },
  "tower_config": {
    "base_costs": {"basic": 20, "sniper": 40, "cannon": 60, etc}
  }
}

OUTPUT ONLY THE COMPLETE JSON - NO OTHER TEXT.
```

### Expected AI Output (Full Generation)
The AI should return a complete JSON configuration like this:

```json
{
  "level_name": "Frozen Lakes Trial",
  "game_config": {
    "starting_lives": 20,
    "starting_money": 500,
    "lives_per_wave": 0
  },
  "map_config": {
    "default_map": {
      "width": 20,
      "height": 15,
      "terrain": [
        [2,2,2,0,0,3,3,3,0,0,4,4,4,0,0,2,2,2,0,0],
        [2,0,0,0,1,1,1,3,3,0,4,4,4,0,0,0,0,2,0,0],
        // ... complete terrain grid
      ],
      "path": [[4,0], [4,1], [5,1], [6,1], [7,1], [7,2], [7,3], ...]
    }
  },
  "wave_config": {
    "total_waves": 80,
    "spawn_config": {
      "base_enemy_count": 8,
      "base_spawn_delay": 90,
      "enemy_increase_per_round": {"wave_ranges": {"1-10": 2, "11-20": 3}, "default": 1}
    },
    "wave_compositions": {
      "1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.2]],
      "6-10": [["BasicEnemy", 0.6], ["FastEnemy", 0.2], ["TankEnemy", 0.2]]
    },
    "boss_waves": {
      "10": "SpeedBoss",
      "20": "MegaBoss",
      "80": "TimeLordBoss"
    },
    "enemy_buffs": {
      "max_health_modifier": 1.0,
      "max_speed_modifier": 1.0
    }
  },
  "tower_config": {
    "base_costs": {
      "basic": 20,
      "sniper": 40,
      "cannon": 60,
      "ice": 50,
      "splash": 80
    }
  }
}
```

## 2. SMART DELEGATION FALLBACK SYSTEM

### System Message (Input)
```
You are an expert game designer creating detailed procedural generation instructions. Output only JSON.
```

### User Prompt Template (Input)
```
Create DETAILED instructions for procedural map generation based on player performance:

PLAYER PERFORMANCE:
Score: {score}/100
Victory: {'Yes' if win_flag else 'No'}
Lives Remaining: {lives_remaining}/{starting_lives}
Tower Diversity: {tower_diversity} types used

Provide detailed generation instructions in JSON format only:
```

### Expected AI Output (Smart Delegation)
```json
{
  "difficulty_target": 55,
  "terrain_strategy": "balanced_challenge",
  "path_complexity": 0.6,
  "enemy_focus": ["TankEnemy", "FastEnemy"],
  "special_mechanics": ["water_placement", "chokepoints"],
  "reasoning": "Player shows moderate skill, needs slight difficulty increase with diverse terrain"
}
```

## 3. MODULAR AI GENERATION SYSTEM

### A. Terrain Strategy Generation

#### System Message (Input)
```
You are a terrain designer. Output only JSON, no other text.
```

#### User Prompt Template (Input)
```
Create terrain strategy for tower defense map based on player performance and previous terrain configuration.

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}
- Tower Diversity: {tower_diversity} types

PREVIOUS TERRAIN CONFIGURATION:
• Map Size: {width}x{height}
• Terrain Config: {previous_terrain}
• Special Mechanics: {special_mechanics}

DIFFICULTY CONTEXT:
- Player scored {score}% on difficulty {previous_difficulty} level
- Previous map configuration: {width}x{height}
- This suggests {'high competence' if score >= 70 and previous_difficulty >= 60 else 'moderate competence' if score >= 50 else 'developing skills'}

Output ONLY this JSON format:
{
    "path_type": "normal_path",
    "terrain_reasoning": "strategic terrain placement rationale"
}
```

#### Expected AI Output (Terrain Strategy)
```json
{
  "path_type": "normal_path",
  "terrain_reasoning": "Given the player's performance and lack of victory, the terrain will be adjusted to provide a more straightforward path that encourages the use of diverse towers. The map will maintain a standard configuration but will include strategic choke points to allow the player to leverage their tower diversity effectively, promoting skill development without overwhelming challenges."
}
```

### B. Enemy Strategy Generation

#### System Message (Input)
```
You are an enemy strategist. Output only JSON, no other text.
```

#### User Prompt Template (Input)
```
Create enemy counter-strategy based on player performance and previous enemy configuration.

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}
- Tower Diversity: {tower_diversity} types
- Lives Remaining: {lives_remaining}/{starting_lives}

PREVIOUS ENEMY CONFIGURATION:
• Enemy Types Used: {previous_enemy_types}
• Total Enemy Types: {len(previous_enemy_types)}
• Wave Count: {total_waves}
• Boss Waves: {boss_waves}

DIFFICULTY CONTEXT:
- Player scored {score}% on difficulty {previous_difficulty} level
- This suggests {'high competence' if score >= 70 else 'moderate competence' if score >= 50 else 'developing skills'}

ENEMY DIFFICULTY REFERENCE (Difficulty 70 baseline):
- BasicEnemy: 15 difficulty points
- FastEnemy: 20 difficulty points
- TankEnemy: 35 difficulty points
- FlyingEnemy: 40 difficulty points
- ShieldedEnemy: 45 difficulty points
- ArmoredEnemy: 50 difficulty points
- InvisibleEnemy: 55 difficulty points
- EnergyShieldEnemy: 60 difficulty points
- GroundedEnemy: 45 difficulty points
- RegeneratingEnemy: 65 difficulty points
- TeleportingEnemy: 70 difficulty points
- FireElementalEnemy: 75 difficulty points
- ToxicEnemy: 60 difficulty points
- SplittingEnemy: 80 difficulty points
- PhaseShiftEnemy: 85 difficulty points
- BlastProofEnemy: 70 difficulty points
- SpectralEnemy: 90 difficulty points
- CrystallineEnemy: 85 difficulty points
- ToxicMutantEnemy: 95 difficulty points
- VoidEnemy: 100 difficulty points
- AdaptiveEnemy: 110 difficulty points

TOWER EFFECTIVENESS MATRIX:
- Basic Tower: Effective vs BasicEnemy, FastEnemy (weak vs armored)
- Sniper Tower: Effective vs FlyingEnemy, InvisibleEnemy (weak vs shields)
- Cannon Tower: Effective vs TankEnemy, ArmoredEnemy (weak vs fast)
- Ice Tower: Effective vs FastEnemy, TeleportingEnemy (weak vs fire)
- Splash Tower: Effective vs SplittingEnemy, groups (weak vs single targets)
- Missile Tower: Effective vs FlyingEnemy, ShieldedEnemy (weak vs fast)
- Laser Tower: Effective vs EnergyShieldEnemy, SpectralEnemy (weak vs reflective)
- Poison Tower: Effective vs RegeneratingEnemy, TankEnemy (weak vs ToxicMutant)

STRATEGIC ENEMY SELECTION RULES:
Based on player's tower diversity and performance, select counter-enemies:

If Tower Diversity = 0-2 types (FORCE EXPERIMENTATION):
→ Select enemies that REQUIRE different tower types to counter effectively
→ Primary counters: Enemies weak to towers the player DOESN'T use
→ Force adaptation by making their preferred towers less effective

If Lives Remaining < 50% (STRUGGLING PLAYER):
→ Select gentler counter-enemies that teach without overwhelming
→ Focus on educational counters with moderate presence
→ Avoid extreme difficulty spikes

If Score >= 80% AND Won (HIGH SKILL):
→ Select challenging counter-enemies that require advanced strategies
→ Use high-difficulty enemies with complex mechanics
→ Create intense, skill-testing encounters

EXTREME SPAWN DISTRIBUTION REQUIREMENTS:
You must create POLARIZED, EXTREME enemy distributions, not balanced ones:
- Primary enemies should have 40-70% spawn rates (very high)
- Secondary enemies should have 20-35% spawn rates (moderate)
- Rare enemies should have 1-10% spawn rates (very low)
- Create polarized distributions where some enemies dominate while others are rare
- Later waves should be more extreme than early waves
- Use spawn odds that create focused, intense encounters rather than balanced distributions

EXAMPLE EXTREME DISTRIBUTIONS:
Early waves (1-30): Moderate extremes (20-40% primary, 10-25% secondary, 5-15% rare)
Mid waves (31-60): High extremes (30-50% primary, 15-30% secondary, 2-10% rare)
Late waves (61-80): MAXIMUM extremes (40-70% primary, 20-35% secondary, 1-5% rare)

Output ONLY this JSON format:
{
    "primary_counter_enemies": ["EnemyName1", "EnemyName2"],
    "strategy_focus": "brief description based on performance level",
    "extreme_spawn_preference": "late_game_focus",
    "spawn_distribution_style": "polarized_extreme"
}
```

#### Expected AI Output (Enemy Strategy)
```json
{
  "primary_counter_enemies": ["AdaptiveEnemy", "InvisibleEnemy"],
  "strategy_focus": "Gentle educational counters with light presence to encourage adaptation",
  "extreme_spawn_preference": "late_game_focus",
  "spawn_distribution_style": "polarized_extreme"
}
```

## 4. EXAMPLE COMPLETE AI INTERACTION

### Sample Input Data
```json
{
  "score": 65.0,
  "win_flag": true,
  "lives_remaining": 12,
  "starting_lives": 20,
  "towers_built": {
    "basic": 8,
    "sniper": 4,
    "cannon": 2,
    "ice": 3
  },
  "tower_diversity": 4,
  "previous_difficulty": 50,
  "resources_spent": 850,
  "resources_earned": 1200,
  "final_wave": 25
}
```

### Complete AI Response Example
```json
{
  "level_name": "Whispering Wisdom",
  "game_config": {
    "starting_lives": 20,
    "starting_money": 600,
    "lives_per_wave": 0
  },
  "map_config": {
    "default_map": {
      "width": 20,
      "height": 15,
      "terrain": [
        [2,2,2,0,0,3,3,3,0,0,4,4,4,0,0,2,2,2,0,0],
        [2,0,0,0,1,1,1,3,3,0,4,4,4,0,0,0,0,2,0,0],
        [0,0,3,3,1,0,0,3,3,0,4,4,4,0,0,3,3,0,0,0],
        [3,3,3,0,1,0,0,0,0,0,0,0,0,0,0,3,3,3,3,0],
        [3,3,0,0,1,0,4,4,4,0,0,0,0,0,0,0,3,3,3,0],
        [0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0],
        [0,4,4,4,0,0,0,0,0,0,0,0,0,0,1,0,4,4,4,0],
        [0,4,4,4,0,3,3,3,0,0,0,0,0,0,1,0,4,4,4,0],
        [0,0,0,0,0,3,3,3,0,2,2,2,0,0,1,0,0,0,0,0],
        [3,3,3,0,0,0,0,0,0,2,2,2,0,0,1,0,3,3,3,3],
        [3,3,3,0,4,4,4,0,0,0,0,0,0,0,1,0,3,3,3,3],
        [0,0,0,0,4,4,4,0,0,0,0,0,0,0,1,0,0,0,0,0],
        [0,0,0,0,0,0,0,0,0,3,3,3,0,0,1,1,1,1,1,1],
        [2,2,2,0,0,0,0,0,0,3,3,3,0,0,0,0,0,0,0,0],
        [2,2,2,0,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0]
      ],
      "path": [
        [4,0], [4,1], [4,2], [4,3], [4,4], [5,5], [6,5], [7,5], [8,5], [9,5],
        [10,5], [11,5], [12,5], [13,5], [14,5], [14,6], [14,7], [14,8], [14,9],
        [14,10], [14,11], [14,12], [15,12], [16,12], [17,12], [18,12], [19,12]
      ]
    }
  },
  "wave_config": {
    "total_waves": 80,
    "spawn_config": {
      "base_enemy_count": 8,
      "base_spawn_delay": 85,
      "enemy_increase_per_round": {
        "wave_ranges": {
          "1-10": 2,
          "11-20": 3,
          "21-30": 4,
          "31-40": 5,
          "41-50": 6,
          "51-60": 7,
          "61-70": 8,
          "71-80": 9
        },
        "default": 1
      }
    },
    "wave_compositions": {
      "1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.14]],
      "6": [["AdaptiveEnemy", 0.35], ["InvisibleEnemy", 0.35], ["BasicEnemy", 0.1], ["FastEnemy", 0.1], ["TankEnemy", 0.1]]
    },
    "boss_waves": {
      "9": "SpeedBoss",
      "18": "SpeedBoss",
      "27": "SpeedBoss",
      "36": "SpeedBoss",
      "45": "SpeedBoss",
      "54": "MegaBoss",
      "63": "MegaBoss",
      "72": "MegaBoss",
      "80": "TimeLordBoss"
    },
    "enemy_buffs": {
      "max_health_modifier": 0.85,
      "max_speed_modifier": 0.9
    }
  },
  "tower_config": {
    "base_costs": {
      "basic": 20,
      "sniper": 40,
      "cannon": 60,
      "ice": 50,
      "splash": 80,
      "missile": 100,
      "laser": 120,
      "poison": 90
    }
  },
  "_generation_metadata": {
    "generation_method": "full_ai_complete",
    "validation_passed": true,
    "ai_confidence": "high",
    "creation_type": "🤖 FULL AI GENERATION"
  },
  "_ai_map_strategy": {
    "path_strategy": "winding",
    "path_length_target": 50,
    "path_complexity": 0.5,
    "terrain_strategy": "balanced",
    "buildable_space_target": 0.6,
    "strategic_focus": "adaptation",
    "layout_reasoning": "The winding path with moderate complexity allows the player time to adapt to increased difficulty without overwhelming pressure, supporting their preferred ice strategy. Balanced terrain offers strategic tower placement without excessive constraints, addressing the player's low win rate while leveraging their progression strength."
  },
  "_ai_terrain_strategy": {
    "path_type": "normal_path",
    "terrain_reasoning": "Given the player's performance and lack of victory, the terrain will be adjusted to provide a more straightforward path that encourages the use of diverse towers. The map will maintain a standard configuration but will include strategic choke points to allow the player to leverage their tower diversity effectively, promoting skill development without overwhelming challenges."
  },
  "_ai_enemy_strategy": {
    "primary_counter_enemies": ["AdaptiveEnemy", "InvisibleEnemy"],
    "strategy_focus": "Gentle educational counters with light presence to encourage adaptation",
    "extreme_spawn_preference": "late_game_focus",
    "spawn_distribution_style": "polarized_extreme"
  }
}
```

## 5. AI CONFIGURATION PARAMETERS

### OpenAI API Settings Used
- **Model**: Azure OpenAI deployment
- **Max Tokens**:
  - Full Generation: 4000 tokens
  - Smart Delegation: 1500 tokens
  - Terrain Strategy: 600 tokens
  - Enemy Strategy: 800 tokens
- **Temperature**: 0.7 (balanced creativity and consistency)
- **Top-p**: Not specified (uses default)
- **Frequency Penalty**: Not specified (uses default)
- **Presence Penalty**: Not specified (uses default)

### Response Processing
1. **JSON Extraction**: Finds first `{` to last `}` in response
2. **Validation**: Checks for required fields and structure
3. **Fallback**: Uses smart delegation if full generation fails
4. **Error Handling**: Falls back to procedural generation if AI fails

This documentation captures all the inputs sent to AI and the expected outputs when generating new levels in the tower defense game.
