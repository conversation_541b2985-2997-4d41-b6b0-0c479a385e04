#!/usr/bin/env python3
"""
Wave Timing Balance Algorithm

This module implements a mathematical algorithm to balance enemy count and spawn rate
to ensure all enemies in a wave spawn within a 60-second (3600 frame) time limit.

The algorithm scales based on difficulty:
- Harder levels: Faster spawn rates, more enemies
- Easier levels: Fewer enemies, moderate spawn rates

Formula ensures optimal balance while respecting the time constraint.
"""

import math
from typing import Dict, Tuple, Any


# Constants
MAX_WAVE_DURATION_FRAMES = 3600  # 60 seconds at 60 FPS
MIN_SPAWN_DELAY_FRAMES = 10      # Minimum 10 frames between spawns (0.167 seconds)
MAX_SPAWN_DELAY_FRAMES = 180     # Maximum 3 seconds between spawns
DIFFICULTY_SCALING_FACTOR = 0.8  # How much difficulty affects the balance


def calculate_balanced_wave_timing(enemy_count: int, difficulty: float, 
                                 wave_number: int) -> Dict[str, Any]:
    """
    Calculate optimal spawn delay and potentially adjusted enemy count to fit within time limit.
    
    Args:
        enemy_count: Desired number of enemies for this wave
        difficulty: Difficulty factor (0-100)
        wave_number: Current wave number for scaling
        
    Returns:
        Dictionary containing:
        - spawn_delay: Optimal spawn delay in frames
        - adjusted_enemy_count: Enemy count (may be reduced if necessary)
        - total_spawn_time: Total time to spawn all enemies
        - balance_strategy: Description of the balancing approach used
    """
    # Normalize difficulty to 0-1 range
    normalized_difficulty = max(0, min(100, difficulty)) / 100.0
    
    # Calculate base spawn delay needed to fit all enemies in time limit
    # Leave some buffer time (10% of max duration) for the last enemy to move
    available_spawn_time = MAX_WAVE_DURATION_FRAMES * 0.9
    base_required_delay = available_spawn_time / max(1, enemy_count - 1)
    
    # Apply difficulty-based scaling
    # Harder difficulties prefer faster spawns, easier prefer fewer enemies
    difficulty_spawn_multiplier = 1.0 - (normalized_difficulty * DIFFICULTY_SCALING_FACTOR)
    target_spawn_delay = base_required_delay * difficulty_spawn_multiplier
    
    # Clamp spawn delay to reasonable bounds
    spawn_delay = max(MIN_SPAWN_DELAY_FRAMES, 
                     min(MAX_SPAWN_DELAY_FRAMES, target_spawn_delay))
    
    # If spawn delay hit the minimum, we need to reduce enemy count
    adjusted_enemy_count = enemy_count
    if target_spawn_delay < MIN_SPAWN_DELAY_FRAMES:
        # Calculate maximum enemies we can spawn with minimum delay
        max_enemies_at_min_delay = int(available_spawn_time / MIN_SPAWN_DELAY_FRAMES) + 1
        adjusted_enemy_count = min(enemy_count, max_enemies_at_min_delay)
        spawn_delay = MIN_SPAWN_DELAY_FRAMES
        balance_strategy = "reduced_enemies_for_min_delay"
    elif target_spawn_delay > MAX_SPAWN_DELAY_FRAMES:
        # Use maximum delay, which naturally reduces spawn rate
        spawn_delay = MAX_SPAWN_DELAY_FRAMES
        balance_strategy = "max_delay_natural_pacing"
    else:
        balance_strategy = "optimal_balance"
    
    # Calculate actual total spawn time
    total_spawn_time = (adjusted_enemy_count - 1) * spawn_delay
    
    # Apply wave progression scaling (later waves get slightly faster)
    wave_scaling = 1.0 - (wave_number * 0.005)  # 0.5% faster per wave
    wave_scaling = max(0.7, wave_scaling)  # Don't go below 70% of calculated delay
    spawn_delay = int(spawn_delay * wave_scaling)
    spawn_delay = max(MIN_SPAWN_DELAY_FRAMES, spawn_delay)
    
    return {
        'spawn_delay': int(spawn_delay),
        'adjusted_enemy_count': adjusted_enemy_count,
        'total_spawn_time': int(total_spawn_time),
        'balance_strategy': balance_strategy,
        'difficulty_factor': normalized_difficulty,
        'wave_scaling_applied': wave_scaling,
        'time_utilization': total_spawn_time / MAX_WAVE_DURATION_FRAMES
    }


def calculate_difficulty_based_enemy_spawn_balance(difficulty: float, wave_number: int,
                                                 base_enemy_count: int) -> Dict[str, Any]:
    """
    Calculate enemy count and spawn timing based on difficulty and wave progression.
    
    This function implements the core balancing algorithm:
    - Easy levels: Moderate enemy count, comfortable spawn rates
    - Hard levels: More enemies, faster spawn rates
    - All levels: Respect 60-second time limit
    
    Args:
        difficulty: Difficulty level (0-100)
        wave_number: Current wave number
        base_enemy_count: Base enemy count from configuration
        
    Returns:
        Dictionary with balanced parameters
    """
    # Normalize difficulty
    normalized_difficulty = max(0, min(100, difficulty)) / 100.0
    
    # Calculate enemy count scaling based on difficulty
    # Easy: 0.7x to 1.0x base count
    # Hard: 1.0x to 2.0x base count
    enemy_count_multiplier = 0.7 + (normalized_difficulty * 1.3)
    
    # Apply wave progression (enemies increase over time)
    wave_progression_multiplier = 1.0 + (wave_number - 1) * 0.1
    
    # Calculate target enemy count
    target_enemy_count = int(base_enemy_count * enemy_count_multiplier * wave_progression_multiplier)
    
    # Get balanced timing for this enemy count
    timing_result = calculate_balanced_wave_timing(target_enemy_count, difficulty, wave_number)
    
    # Combine results
    result = {
        'enemy_count': timing_result['adjusted_enemy_count'],
        'spawn_delay': timing_result['spawn_delay'],
        'total_spawn_time_frames': timing_result['total_spawn_time'],
        'total_spawn_time_seconds': timing_result['total_spawn_time'] / 60.0,
        'balance_strategy': timing_result['balance_strategy'],
        'scaling_factors': {
            'difficulty_multiplier': enemy_count_multiplier,
            'wave_progression_multiplier': wave_progression_multiplier,
            'final_wave_scaling': timing_result['wave_scaling_applied']
        },
        'time_efficiency': timing_result['time_utilization'],
        'within_time_limit': timing_result['total_spawn_time'] <= MAX_WAVE_DURATION_FRAMES
    }
    
    return result


def validate_wave_timing_constraints(enemy_count: int, spawn_delay: int) -> Dict[str, Any]:
    """
    Validate that a given enemy count and spawn delay combination meets timing constraints.
    
    Args:
        enemy_count: Number of enemies to spawn
        spawn_delay: Delay between spawns in frames
        
    Returns:
        Dictionary with validation results
    """
    total_spawn_time = (enemy_count - 1) * spawn_delay
    within_limit = total_spawn_time <= MAX_WAVE_DURATION_FRAMES
    
    return {
        'valid': within_limit,
        'total_spawn_time': total_spawn_time,
        'total_spawn_time_seconds': total_spawn_time / 60.0,
        'time_limit_seconds': MAX_WAVE_DURATION_FRAMES / 60.0,
        'time_remaining': MAX_WAVE_DURATION_FRAMES - total_spawn_time,
        'utilization_percentage': (total_spawn_time / MAX_WAVE_DURATION_FRAMES) * 100
    }


# Example usage and testing
if __name__ == "__main__":
    print("=== Wave Timing Balance Algorithm Test ===")
    
    # Test different difficulty levels
    test_cases = [
        (10, 25, 1),   # Easy level, wave 1
        (50, 25, 10),  # Medium level, wave 10
        (90, 25, 20),  # Hard level, wave 20
        (100, 50, 50), # Very hard level, wave 50
    ]
    
    for difficulty, base_count, wave in test_cases:
        result = calculate_difficulty_based_enemy_spawn_balance(difficulty, wave, base_count)
        print(f"\nDifficulty {difficulty}, Wave {wave}, Base Count {base_count}:")
        print(f"  Final Enemy Count: {result['enemy_count']}")
        print(f"  Spawn Delay: {result['spawn_delay']} frames ({result['spawn_delay']/60:.2f}s)")
        print(f"  Total Spawn Time: {result['total_spawn_time_seconds']:.1f}s")
        print(f"  Strategy: {result['balance_strategy']}")
        print(f"  Time Efficiency: {result['time_efficiency']:.1%}")
        print(f"  Within Limit: {result['within_time_limit']}")
