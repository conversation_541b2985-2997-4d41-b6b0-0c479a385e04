#!/usr/bin/env python3
"""
Test script to verify that only the modular AI generator is used and working correctly.
This script tests the adaptive config generator with modular AI only.
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_modular_ai_only():
    """Test that the system only uses modular AI and works correctly"""
    print("="*80)
    print("🧩 TESTING MODULAR AI ONLY SYSTEM")
    print("="*80)

    try:
        from ai.adaptive_config_generator import AdaptiveConfigGenerator

        # Create the generator with only modular AI (default now)
        generator = AdaptiveConfigGenerator(use_modular_ai=True)

        print("✅ AdaptiveConfigGenerator created successfully")
        print(f"   Modular AI enabled: {generator.use_modular_ai}")
        print(f"   AI available: {generator.ai_available}")

        # Verify that full AI generator is not available
        if hasattr(generator, 'full_ai_generator'):
            if generator.full_ai_generator is None:
                print("✅ Full AI generator is properly disabled (None)")
            else:
                print("⚠️ Full AI generator still exists")
        else:
            print("✅ Full AI generator attribute removed")

        # Test that modular AI generator is available
        if hasattr(generator, 'modular_ai_generator'):
            if generator.modular_ai_generator is not None:
                print("✅ Modular AI generator is available")
            else:
                print("⚠️ Modular AI generator is None (may be due to missing API key)")
        else:
            print("❌ Modular AI generator attribute missing")

        # Test the generate_config_from_recent_games method
        print("\n🧩 Testing generate_config_from_recent_games method...")
        try:
            config = generator.generate_config_from_recent_games()
            if config:
                print("✅ generate_config_from_recent_games returned a config")
                print(f"   Level name: {config.get('level_name', 'No name')}")

                # Check for modular AI metadata
                if '_adaptive_metadata' in config:
                    metadata = config['_adaptive_metadata']
                    generation_method = metadata.get(
                        'generation_method', 'unknown')
                    print(f"   Generation method: {generation_method}")

                    if 'modular_ai' in generation_method:
                        print("✅ Config was generated using modular AI")
                    else:
                        print(
                            f"⚠️ Config generation method: {generation_method}")
                else:
                    print("⚠️ No adaptive metadata found in config")
            else:
                print(
                    "⚠️ generate_config_from_recent_games returned None (may be due to no performance data)")
        except Exception as e:
            print(f"⚠️ generate_config_from_recent_games failed: {e}")

        print("\n✅ MODULAR AI ONLY SYSTEM TEST COMPLETED")
        return True

    except Exception as e:
        print(f"❌ Error testing modular AI system: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_imports():
    """Test that full AI generator import fails as expected"""
    print("\n" + "="*80)
    print("📦 TESTING IMPORT CLEANUP")
    print("="*80)

    # Test that full AI generator is no longer importable
    try:
        from ai.full_ai_generator import FullAIGenerator
        print("❌ full_ai_generator is still importable - cleanup incomplete")
        return False
    except ImportError:
        print("✅ full_ai_generator is no longer importable - cleanup successful")
    except Exception as e:
        print(f"⚠️ Unexpected error importing full_ai_generator: {e}")

    # Test that modular AI generator is still importable
    try:
        from ai.modular_ai_generator import ModularAIGenerator
        print("✅ modular_ai_generator is importable")
        return True
    except ImportError as e:
        print(f"❌ modular_ai_generator import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error importing modular_ai_generator: {e}")
        return False


def test_game_launcher_integration():
    """Test that game launcher uses only modular AI"""
    print("\n" + "="*80)
    print("🎮 TESTING GAME LAUNCHER INTEGRATION")
    print("="*80)

    try:
        # Check the game launcher code
        with open('game_launcher.py', 'r') as f:
            content = f.read()

        # Look for the adaptive config generation call
        if 'use_modular_ai=True' in content and 'use_full_ai' not in content:
            print("✅ Game launcher uses modular AI only (full AI references removed)")
        elif 'use_full_ai' in content:
            print("⚠️ Game launcher still has full AI references")
        else:
            print("⚠️ Game launcher AI configuration unclear")

        return True

    except Exception as e:
        print(f"❌ Error checking game launcher: {e}")
        return False


def main():
    """Main function to run all tests"""
    print("🔍 MODULAR AI ONLY VERIFICATION SCRIPT")
    print("This script verifies that the system only uses modular AI generation.")
    print("="*80)

    # Run all tests
    results = []
    results.append(test_imports())
    results.append(test_modular_ai_only())
    results.append(test_game_launcher_integration())

    print("\n" + "="*80)
    print("🏁 VERIFICATION COMPLETED")
    print("="*80)

    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("🧩 System successfully configured to use MODULAR AI ONLY")
    else:
        print(f"⚠️ SOME TESTS FAILED ({passed}/{total})")
        print("🔧 Additional cleanup may be needed")

    print("\nModular AI System Features:")
    print("  🧩 AI-generated terrain strategies")
    print("  🎯 AI-generated enemy counter-strategies")
    print("  🏗️ Procedural framework with AI guidance")
    print("  📊 Performance-based adaptive difficulty")


if __name__ == "__main__":
    main()
