import pygame
from typing import List, Tuple
from .enemy import Enemy

class ShieldedEnemy(Enemy):
    """Enemy with regenerating shields"""
    def __init__(self, path: List[Tuple[int, int]], wave_number: int = 1):
        super().__init__(path, wave_number)
        self.max_health = 15
        self.health = self.max_health
        self.max_shield = 15
        self.shield = self.max_shield
        self.shield_regen_timer = 0
        self.shield_regen_delay = 180  # 3 seconds at 60 FPS
        self.speed = 1.2
        self.reward = 10
        self.size = 10
        self.color = (0, 255, 255)  # <PERSON>an
    
    def update(self):
        """Update with shield regeneration"""
        super().update()

        # Regenerate shield
        if self.shield < self.max_shield:
            self.shield_regen_timer += 1
            if self.shield_regen_timer >= self.shield_regen_delay:
                self.shield += 1
                self.shield_regen_timer = 0
    
    def take_damage(self, damage: int, tower_type: str = 'basic'):
        """Damage goes to shield first, then health"""
        original_total_hp = self.shield + self.health
        
        # Apply damage to shield first, then health
        if self.shield > 0:
            shield_damage = min(self.shield, damage)
            self.shield -= shield_damage
            damage -= shield_damage
            self.shield_regen_timer = 0  # Reset regen timer when shield is hit
        
        # Apply remaining damage to health
        if damage > 0:
            health_damage = min(self.health, damage)
            self.health -= health_damage
        
        # Calculate actual total damage dealt
        new_total_hp = max(0, self.shield + self.health)
        actual_damage = original_total_hp - new_total_hp
        return actual_damage
    
    def draw(self, screen: pygame.Surface):
        """Draw enemy with shield indicator"""
        super().draw(screen)

        # Draw shield bar above health bar
        bar_width = self.size * 2
        bar_height = 3
        bar_x = int(self.x - bar_width // 2)
        bar_y = int(self.y - self.size - 20)  # Position above health bar

        # Always draw shield bar background (even when shield is 0)
        pygame.draw.rect(screen, (60, 60, 60), (bar_x, bar_y, bar_width, bar_height))

        # Draw shield amount
        if self.shield > 0:
            shield_width = int((self.shield / self.max_shield) * bar_width)
            pygame.draw.rect(screen, (0, 255, 255), (bar_x, bar_y, shield_width, bar_height))

        # Draw shield border
        pygame.draw.rect(screen, (255, 255, 255), (bar_x, bar_y, bar_width, bar_height), 1)

        # Draw shield regeneration indicator
        if self.shield < self.max_shield and self.shield_regen_timer > 0:
            # Show regeneration progress as a small indicator
            regen_progress = self.shield_regen_timer / self.shield_regen_delay
            indicator_width = int(bar_width * regen_progress)
            pygame.draw.rect(screen, (100, 255, 255), (bar_x, bar_y - 2, indicator_width, 1))
