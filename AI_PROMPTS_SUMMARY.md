# AI Level Generation - Prompts Summary

## Overview
The tower defense game uses a **Modular AI Generation System** to generate new levels based on player performance. Here are the key AI prompts and their purposes:

## 1. TERRAIN STRATEGY GENERATION - Modular AI

**Purpose**: Generate complete level configuration including terrain, enemies, and waves

**Key Input Variables**:
- Player score (0-100)
- Win/loss status
- Lives remaining vs starting lives
- Tower diversity (number of different tower types used)
- Previous difficulty level

**Main Prompt Structure**:
```
You are an expert tower defense game designer. Generate a COMPLETE game configuration based on this player performance:

PLAYER PERFORMANCE ANALYSIS:
Score: {score}/100
Victory: {win_flag}
Lives Remaining: {lives_remaining}/{starting_lives}
Tower Diversity: {tower_diversity} different types used
Previous Config Difficulty: {previous_difficulty}

[Detailed terrain mechanics and placement rules based on performance]
[Enemy health/speed modification rules based on score]
[Required JSON output structure]

OUTPUT ONLY THE COMPLETE JSON - NO OTHER TEXT.
```

**Expected Output**: Complete JSON configuration with level_name, game_config, map_config, wave_config, tower_config

## 2. SMART DELEGATION - Fallback Prompt

**Purpose**: Generate instructions for procedural generation when full AI fails

**Prompt**:
```
Create DETAILED instructions for procedural map generation based on player performance:

PLAYER PERFORMANCE:
Score: {score}/100
Victory: {win_flag}
Lives Remaining: {lives_remaining}/{starting_lives}
Tower Diversity: {tower_diversity} types used

Provide detailed generation instructions in JSON format only:
```

**Expected Output**: JSON with difficulty_target, terrain_strategy, path_complexity, enemy_focus, etc.

## 3. TERRAIN STRATEGY - Modular AI

**Purpose**: Generate terrain placement strategy based on player performance

**Prompt**:
```
Create terrain strategy for tower defense map based on player performance and previous terrain configuration.

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}
- Tower Diversity: {tower_diversity} types

[Previous terrain configuration context]
[Difficulty assessment]

Output ONLY this JSON format:
{
    "path_type": "normal_path",
    "terrain_reasoning": "strategic terrain placement rationale"
}
```

**Expected Output**: JSON with path_type and terrain_reasoning

## 4. ENEMY STRATEGY - Modular AI

**Purpose**: Select counter-enemies based on player's tower usage and performance

**Prompt**:
```
Create enemy counter-strategy based on player performance and previous enemy configuration.

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}
- Tower Diversity: {tower_diversity} types
- Lives Remaining: {lives_remaining}/{starting_lives}

[Previous enemy configuration]
[Enemy difficulty reference table]
[Tower effectiveness matrix]
[Strategic enemy selection rules]
[Extreme spawn distribution requirements]

Output ONLY this JSON format:
{
    "primary_counter_enemies": ["EnemyName1", "EnemyName2"],
    "strategy_focus": "brief description based on performance level",
    "extreme_spawn_preference": "late_game_focus",
    "spawn_distribution_style": "polarized_extreme"
}
```

**Expected Output**: JSON with primary_counter_enemies, strategy_focus, spawn preferences

## Key AI Decision Rules

### Terrain Placement Rules
- **Low Tower Diversity (0-2 types)**: Force experimentation with 35-40% Water, 25-30% Forest terrain
- **Low Lives Remaining (<50%)**: Easier challenge with 30-35% Water for freeze towers
- **High Score (≥80%)**: Difficult challenge with Sand paths (50% faster enemies)

### Enemy Health/Speed Modifiers
- **Score 0-30% + Lost**: 0.7x health, 0.8x speed (easier)
- **Score 31-50% + Lost**: 0.85x health, 0.9x speed (slightly easier)
- **Score 51-70% + Won**: 1.0x health, 1.0x speed (same)
- **Score 71-85% + Won**: 1.2x health, 1.15x speed (harder)
- **Score 86-100% + Won**: 1.4x health, 1.3x speed (much harder)

### Enemy Selection Strategy
- **Low Diversity**: Select enemies requiring different tower types
- **Struggling Players**: Gentle educational counters
- **High Skill**: Challenging enemies with complex mechanics

## AI Configuration
- **Model**: Azure OpenAI
- **Temperature**: 0.7
- **Max Tokens**: 4000 (full), 1500 (delegation), 600-800 (modular)
- **Fallback Chain**: Full AI → Smart Delegation → Procedural Generation

## Sample Performance Data Input
```json
{
  "score": 65.0,
  "win_flag": true,
  "lives_remaining": 12,
  "starting_lives": 20,
  "towers_built": {
    "basic": 8,
    "sniper": 4,
    "cannon": 2,
    "ice": 3
  },
  "tower_diversity": 4,
  "previous_difficulty": 50
}
```

## Sample AI Output
```json
{
  "level_name": "Whispering Wisdom",
  "game_config": {
    "starting_lives": 20,
    "starting_money": 600,
    "lives_per_wave": 0
  },
  "map_config": {
    "default_map": {
      "width": 20,
      "height": 15,
      "terrain": [[2,2,2,0,0,3,3,3,0,0,4,4,4,0,0,2,2,2,0,0], ...],
      "path": [[4,0], [4,1], [4,2], ...]
    }
  },
  "wave_config": {
    "total_waves": 80,
    "spawn_config": {"base_enemy_count": 8, "base_spawn_delay": 85},
    "wave_compositions": {
      "1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.14]],
      "6": [["AdaptiveEnemy", 0.35], ["InvisibleEnemy", 0.35], ...]
    },
    "boss_waves": {"9": "SpeedBoss", "18": "SpeedBoss", "80": "TimeLordBoss"},
    "enemy_buffs": {"max_health_modifier": 0.85, "max_speed_modifier": 0.9}
  },
  "tower_config": {
    "base_costs": {"basic": 20, "sniper": 40, "cannon": 60, "ice": 50, "splash": 80}
  }
}
```

This system creates adaptive, personalized levels that respond to how the player performed in previous games, encouraging skill development and maintaining appropriate challenge levels.
