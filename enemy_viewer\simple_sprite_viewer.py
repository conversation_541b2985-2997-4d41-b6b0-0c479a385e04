#!/usr/bin/env python3
"""
Simple Enemy Sprite Viewer - Frame by Frame
Just shows the enemy sprite on a blank background with frame stepping.
"""

import sys
import os
import pygame
from typing import List, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import enemy classes
import enemies

class SimpleSpriteViewer:
    """Minimal sprite viewer - just enemy sprite and frame stepping"""
    
    def __init__(self, enemy_type: str = "basic"):
        pygame.init()
        
        # Simple screen setup
        self.SCREEN_WIDTH = 800
        self.SCREEN_HEIGHT = 600
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption(f"Sprite Viewer - {enemy_type}")
        
        self.clock = pygame.time.Clock()
        self.running = True
        
        # Frame control
        self.frame_number = 0
        
        # Create enemy
        self.enemy_type = enemy_type
        self.enemy = self.create_enemy()
        
        # Font for frame display
        self.font = pygame.font.Font(None, 36)
        
    def create_enemy(self):
        """Create enemy instance"""
        # Simple path (enemy won't move)
        path = [(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2)]
        
        # Enemy type mapping
        enemy_classes = {
            'basic': enemies.BasicEnemy,
            'fast': enemies.FastEnemy,
            'tank': enemies.TankEnemy,
            'shielded': enemies.ShieldedEnemy,
            'invisible': enemies.InvisibleEnemy,
            'flying': enemies.FlyingEnemy,
            'regenerating': enemies.RegeneratingEnemy,
            'splitting': enemies.SplittingEnemy,
            'teleporting': enemies.TeleportingEnemy,
            'armored': enemies.ArmoredEnemy,
            'energy_shield': enemies.EnergyShieldEnemy,
            'grounded': enemies.GroundedEnemy,
            'fire_elemental': enemies.FireElementalEnemy,
            'toxic': enemies.ToxicEnemy,
            'phase_shift': enemies.PhaseShiftEnemy,
            'blast_proof': enemies.BlastProofEnemy,
            'spectral': enemies.SpectralEnemy,
            'crystalline': enemies.CrystallineEnemy,
            'toxic_mutant': enemies.ToxicMutantEnemy,
            'void': enemies.VoidEnemy,
            'adaptive': enemies.AdaptiveEnemy,
            'mega_boss': enemies.MegaBoss,
            'speed_boss': enemies.SpeedBoss,
            'timelord_boss': enemies.TimeLordBoss,
            'necromancer_boss': enemies.NecromancerBoss,
            'shadow_king': enemies.ShadowKing,
            'crystal_overlord': enemies.CrystalOverlord
        }
        
        enemy_class = enemy_classes.get(self.enemy_type, enemies.BasicEnemy)
        enemy = enemy_class(path, wave_number=1)
        
        # Position in center
        enemy.x = self.SCREEN_WIDTH // 2
        enemy.y = self.SCREEN_HEIGHT // 2
        
        return enemy
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_RIGHT:
                    # Forward one frame
                    self.frame_number += 1
                    self.update_enemy_frame()
                elif event.key == pygame.K_LEFT:
                    # Backward one frame
                    if self.frame_number > 0:
                        self.frame_number -= 1
                        self.reset_enemy_to_frame()
                elif event.key == pygame.K_r:
                    # Reset to frame 0
                    self.frame_number = 0
                    self.reset_enemy_to_frame()
                elif event.key == pygame.K_ESCAPE:
                    self.running = False
    
    def update_enemy_frame(self):
        """Update enemy by one frame"""
        if hasattr(self.enemy, 'update'):
            self.enemy.update()
    
    def reset_enemy_to_frame(self):
        """Reset enemy and advance to current frame"""
        # Recreate enemy
        self.enemy = self.create_enemy()
        
        # Advance to current frame
        for _ in range(self.frame_number):
            self.update_enemy_frame()
    
    def draw(self):
        """Draw everything"""
        # Blank white background
        self.screen.fill((255, 255, 255))
        
        # Draw enemy sprite only
        self.enemy.draw(self.screen)
        
        # Draw frame number in corner
        frame_text = self.font.render(f"Frame: {self.frame_number}", True, (0, 0, 0))
        self.screen.blit(frame_text, (10, 10))
        
        # Draw controls
        controls_text = self.font.render("← → to step frames, R to reset, ESC to exit", True, (0, 0, 0))
        self.screen.blit(controls_text, (10, self.SCREEN_HEIGHT - 40))
        
        pygame.display.flip()
    
    def run(self):
        """Main loop"""
        print(f"Simple Sprite Viewer - {self.enemy_type}")
        print("Controls:")
        print("  → - Next frame")
        print("  ← - Previous frame") 
        print("  R - Reset to frame 0")
        print("  ESC - Exit")
        
        while self.running:
            self.handle_events()
            self.draw()
            self.clock.tick(60)
        
        pygame.quit()

def main():
    """Main entry point"""
    enemy_type = "tank"
    
    if len(sys.argv) > 1:
        enemy_type = sys.argv[1].lower()
    
    viewer = SimpleSpriteViewer(enemy_type)
    viewer.run()

if __name__ == "__main__":
    main()
