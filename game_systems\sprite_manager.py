"""
Sprite Manager - <PERSON><PERSON> loading and managing game sprites for towers and enemies
"""
import pygame
import os
import math
from typing import Dict, Op<PERSON>, Tu<PERSON>, List
from enum import Enum


class SpriteType(Enum):
    """Types of sprites we can load"""
    TOWER = "tower"
    ENEMY = "enemy"
    PROJECTILE = "projectile"


class Direction(Enum):
    """Directions for enemy sprites"""
    NORTH = "north"
    SOUTH = "south"
    EAST = "east"
    WEST = "west"
    NORTHEAST = "northeast"
    NORTHWEST = "northwest"
    SOUTHEAST = "southeast"
    SOUTHWEST = "southwest"


class SpriteManager:
    """Manages loading, caching, and rendering of game sprites"""

    def __init__(self, enemy_sprite_size: int = 32):
        self.enemy_sprite_size = enemy_sprite_size

        # Sprite caches
        self.tower_sprites: Dict[str, pygame.Surface] = {}
        self.rotated_tower_cache: Dict[Tuple[str, int], pygame.Surface] = {}
        # {enemy_type: {direction: surface}}
        self.enemy_sprites: Dict[str, Dict[str, pygame.Surface]] = {}
        self.projectile_sprites: Dict[str, pygame.Surface] = {}

        # Directories
        self.tower_sprites_dir = "assets/tower_sprites"
        self.enemy_sprites_dir = "assets/enemy_sprites"
        self.projectile_sprites_dir = "assets/projectile_sprites"

        # Load flags
        self.sprites_loaded = False

        # Tower type mapping (same as existing system)
        self.tower_types = [
            'basic', 'sniper', 'freezer', 'detector', 'antiair', 'poison',
            'laser', 'cannon', 'lightning', 'flame', 'ice', 'explosive',
            'missile', 'splash', 'destroyer'
        ]

        # Dynamic tower sprite sizes based on importance and grid size
        self.tower_sprite_sizes = {
            # Small towers (1x1) - Basic units
            'basic': 40,
            'antiair': 42,
            'poison': 42,
            'laser': 44,
            'lightning': 44,
            'flame': 42,
            'ice': 42,
            'splash': 40,
            'sniper': 46,      # Specialized, slightly larger
            'freezer': 44,

            # Medium towers (2x2) - Advanced units
            'detector': 56,    # Support tower, bigger presence
            'cannon': 64,      # Heavy artillery
            'missile': 60,     # Missile battery

            # Large towers (3x3) - Ultimate weapons
            'explosive': 80,   # Massive siege weapon
            'destroyer': 96    # Naval battleship - biggest!
        }

        # Enemy types (add more as needed)
        self.enemy_types = [
            'basic', 'fast', 'tank', 'flying', 'armored', 'shielded',
            'invisible', 'regenerating', 'teleporting', 'splitting',
            'energy_shield', 'grounded', 'fire_elemental', 'toxic',
            'blast_proof', 'crystalline', 'adaptive', 'toxic_mutant',
            'void', 'phase_shift', 'spectral', 'mega_boss', 'speed_boss',
            'necromancer_boss', 'timelord_boss', 'shadow_king', 'crystal_overlord'
        ]

        # Expected filenames
        self.tower_sprite_filenames = {
            'basic': 'basic_tower.png',
            'sniper': 'sniper_tower.png',
            'freezer': 'freezer_tower.png',
            'detector': 'detector_tower.png',
            'antiair': 'antiair_tower.png',
            'poison': 'poison_tower.png',
            'laser': 'laser_tower.png',
            'cannon': 'cannon_tower.png',
            'lightning': 'lightning_tower.png',
            'flame': 'flame_tower.png',
            'ice': 'ice_tower.png',
            'explosive': 'explosive_tower.png',
            'missile': 'missile_tower.png',
            'splash': 'splash_tower.png',
            'destroyer': 'destroyer_tower.png'
        }

    def get_tower_sprite_size(self, tower_type: str) -> int:
        """Get the appropriate sprite size for a tower type"""
        return self.tower_sprite_sizes.get(tower_type, 48)  # Default 48 if not found

    def load_all_sprites(self):
        """Load all sprites from their respective directories"""
        if self.sprites_loaded:
            return

        # Load tower sprites
        self._load_tower_sprites()

        # Load enemy sprites
        self._load_enemy_sprites()

        # Load projectile sprites
        self._load_projectile_sprites()

        self.sprites_loaded = True

    def _load_tower_sprites(self):
        """Load tower sprites for in-game rendering with dynamic sizing"""
        # Create directory if it doesn't exist
        if not os.path.exists(self.tower_sprites_dir):
            os.makedirs(self.tower_sprites_dir)
            print(f"Created tower sprites directory: {self.tower_sprites_dir}")
            print("You can place larger tower PNG files here for in-game rendering")
            print("(The existing tower_icons are used for UI only)")

        # Check if we can fall back to existing tower icons
        fallback_dir = "assets/tower_icons"

        for tower_type, filename in self.tower_sprite_filenames.items():
            sprite_loaded = False
            sprite_size = self.get_tower_sprite_size(tower_type)

            # Try loading from tower_sprites directory first
            sprite_path = os.path.join(self.tower_sprites_dir, filename)
            if self._try_load_image(sprite_path, tower_type, sprite_size, self.tower_sprites):
                sprite_loaded = True

            # Fall back to tower_icons if no dedicated sprite
            if not sprite_loaded:
                fallback_path = os.path.join(fallback_dir, filename)
                if self._try_load_image(fallback_path, tower_type, sprite_size, self.tower_sprites):
                    sprite_loaded = True

            # Create fallback if still no sprite
            if not sprite_loaded:
                print(
                    f"No sprite found for {tower_type}, creating fallback at {sprite_size}x{sprite_size} pixels")
                self._create_fallback_tower_sprite(tower_type)

    def _load_enemy_sprites(self):
        """Load enemy sprites with directional support"""
        # Create directory if it doesn't exist
        if not os.path.exists(self.enemy_sprites_dir):
            os.makedirs(self.enemy_sprites_dir)
            print(f"Created enemy sprites directory: {self.enemy_sprites_dir}")
            print("Place enemy PNG files here. Supported naming:")
            print("  Single sprite: enemy_name.png (e.g., basic.png)")
            print("  Directional: enemy_name_direction.png (e.g., basic_east.png)")
            print("  Animation: enemy_name_direction_frame.png (e.g., basic_east_1.png)")

        for enemy_type in self.enemy_types:
            self.enemy_sprites[enemy_type] = {}

            # Try to load directional sprites first
            directions_loaded = 0
            for direction in Direction:
                direction_name = direction.value

                # Try different naming conventions
                possible_names = [
                    f"{enemy_type}_{direction_name}.png",
                    f"{enemy_type}_{direction_name}_1.png",  # First frame
                    f"{enemy_type}_{direction_name}_idle.png"
                ]

                for sprite_name in possible_names:
                    sprite_path = os.path.join(
                        self.enemy_sprites_dir, sprite_name)
                    if os.path.exists(sprite_path):
                        try:
                            sprite = pygame.image.load(sprite_path)
                            sprite = pygame.transform.scale(
                                sprite, (self.enemy_sprite_size, self.enemy_sprite_size))
                            self.enemy_sprites[enemy_type][direction_name] = sprite
                            directions_loaded += 1
                            break
                        except pygame.error as e:
                            print(f"Failed to load {sprite_path}: {e}")

            # If no directional sprites found, try loading a single sprite
            if directions_loaded == 0:
                single_sprite_names = [
                    f"{enemy_type}.png",
                    f"{enemy_type}_idle.png",
                    f"{enemy_type}_default.png"
                ]

                sprite_loaded = False
                for sprite_name in single_sprite_names:
                    sprite_path = os.path.join(
                        self.enemy_sprites_dir, sprite_name)
                    if os.path.exists(sprite_path):
                        try:
                            sprite = pygame.image.load(sprite_path)
                            sprite = pygame.transform.scale(
                                sprite, (self.enemy_sprite_size, self.enemy_sprite_size))

                            # Use same sprite for all directions
                            for direction in Direction:
                                self.enemy_sprites[enemy_type][direction.value] = sprite

                            sprite_loaded = True
                            break
                        except pygame.error as e:
                            print(f"Failed to load {sprite_path}: {e}")

                # Create fallback if no sprite found
                if not sprite_loaded:
                    self._create_fallback_enemy_sprite(enemy_type)

    def _load_projectile_sprites(self):
        """Load projectile sprites"""
        if not os.path.exists(self.projectile_sprites_dir):
            os.makedirs(self.projectile_sprites_dir)

        # Projectile types
        projectile_types = ['basic', 'sniper', 'freeze',
                            'homing', 'ice', 'splash', 'water']

        for proj_type in projectile_types:
            sprite_path = os.path.join(
                self.projectile_sprites_dir, f"{proj_type}_projectile.png")
            if os.path.exists(sprite_path):
                self._try_load_image(sprite_path, proj_type,
                                     16, self.projectile_sprites)

    def _try_load_image(self, path: str, item_name: str, size: int, target_dict: Dict) -> bool:
        """Try to load an image file with proper pixel-perfect scaling"""
        if os.path.exists(path):
            try:
                sprite = pygame.image.load(path).convert_alpha()

                # Use nearest-neighbor scaling to prevent texture filtering artifacts
                sprite = pygame.transform.scale(sprite, (size, size))

                target_dict[item_name] = sprite
                return True
            except pygame.error as e:
                print(f"Failed to load {path}: {e}")
        return False

    def _create_fallback_tower_sprite(self, tower_type: str):
        """Create a fallback sprite for a tower"""
        sprite_size = self.get_tower_sprite_size(tower_type)
        sprite = pygame.Surface((sprite_size, sprite_size), pygame.SRCALPHA)

        # Basic tower shape with color coding
        colors = {
            'basic': (100, 100, 100),
            'sniper': (0, 100, 0),
            'freezer': (0, 100, 255),
            'cannon': (139, 69, 19),
            'laser': (255, 0, 255)
        }

        color = colors.get(tower_type, (128, 128, 128))
        center = sprite_size // 2

        # Scale details based on sprite size
        radius = max(4, center - 6)
        barrel_width = max(2, sprite_size // 16)
        barrel_height = max(4, sprite_size // 3)

        # Draw simple tower shape
        pygame.draw.circle(sprite, color, (center, center), radius)
        pygame.draw.circle(sprite, (255, 255, 255),
                           (center, center), radius, max(1, sprite_size // 20))

        # Add a simple "barrel" pointing up (scaled)
        barrel_rect = (center - barrel_width, 4,
                       barrel_width * 2, barrel_height)
        pygame.draw.rect(sprite, (64, 64, 64), barrel_rect)

        # Add size indicator for larger towers
        if sprite_size >= 60:
            # Draw additional detail for medium towers
            pygame.draw.circle(sprite, (100, 100, 100),
                               (center, center), radius // 2)
        if sprite_size >= 80:
            # Draw extra detail for large towers
            for i in range(4):
                angle = i * 90
                detail_x = center + \
                    math.cos(math.radians(angle)) * (radius - 3)
                detail_y = center + \
                    math.sin(math.radians(angle)) * (radius - 3)
                pygame.draw.circle(sprite, (200, 200, 200),
                                   (int(detail_x), int(detail_y)), 2)

        self.tower_sprites[tower_type] = sprite

    def _create_fallback_enemy_sprite(self, enemy_type: str):
        """Create a fallback sprite for an enemy"""
        sprite = pygame.Surface(
            (self.enemy_sprite_size, self.enemy_sprite_size), pygame.SRCALPHA)

        # Basic enemy shapes with color coding
        colors = {
            'basic': (255, 100, 100),
            'fast': (255, 255, 100),
            'tank': (100, 255, 100),
            'flying': (100, 100, 255),
            'armored': (128, 128, 128)
        }

        color = colors.get(enemy_type, (200, 100, 100))
        center = self.enemy_sprite_size // 2

        # Draw simple enemy shape
        if 'tank' in enemy_type:
            # Rectangle for tank
            pygame.draw.rect(sprite, color, (4, center - 6,
                             self.enemy_sprite_size - 8, 12))
            pygame.draw.rect(sprite, (255, 255, 255),
                             (4, center - 6, self.enemy_sprite_size - 8, 12), 1)
        elif 'flying' in enemy_type:
            # Ellipse for flying
            pygame.draw.ellipse(
                sprite, color, (4, center - 8, self.enemy_sprite_size - 8, 16))
            pygame.draw.ellipse(
                sprite, (255, 255, 255), (4, center - 8, self.enemy_sprite_size - 8, 16), 1)
        else:
            # Circle for basic enemies
            pygame.draw.circle(sprite, color, (center, center), center - 4)
            pygame.draw.circle(sprite, (255, 255, 255),
                               (center, center), center - 4, 1)

        # Use same sprite for all directions
        for direction in Direction:
            if enemy_type not in self.enemy_sprites:
                self.enemy_sprites[enemy_type] = {}
            self.enemy_sprites[enemy_type][direction.value] = sprite

    def get_tower_sprite(self, tower_type: str, angle: float) -> Optional[pygame.Surface]:
        """Get a rotated tower sprite pointing in the specified direction"""
        if not self.sprites_loaded:
            self.load_all_sprites()

        if tower_type not in self.tower_sprites:
            return None

        # Convert angle to degrees and add offset for North-pointing sprites
        # User sprites point North (up), but rotation system expects East (right)
        # So we add 90 degrees to convert North-pointing to East-pointing base
        angle_degrees = int(math.degrees(angle) + 90) % 360
        cache_key = (tower_type, angle_degrees)

        # Check cache first
        if cache_key in self.rotated_tower_cache:
            return self.rotated_tower_cache[cache_key]

        # Rotate sprite (negative for correct pygame rotation direction)
        original_sprite = self.tower_sprites[tower_type]
        rotated_sprite = pygame.transform.rotate(
            original_sprite, -angle_degrees)

        # Cache the rotated sprite
        self.rotated_tower_cache[cache_key] = rotated_sprite

        return rotated_sprite

    def get_enemy_sprite(self, enemy_type: str, direction: Optional[Direction] = None,
                         movement_angle: Optional[float] = None) -> Optional[pygame.Surface]:
        """Get an enemy sprite facing the appropriate direction"""
        if not self.sprites_loaded:
            self.load_all_sprites()

        if enemy_type not in self.enemy_sprites:
            return None

        # Determine direction from movement angle if not specified
        if direction is None and movement_angle is not None:
            direction = self._angle_to_direction(movement_angle)
        elif direction is None:
            direction = Direction.EAST  # Default direction

        enemy_sprites = self.enemy_sprites[enemy_type]

        # Try to get the exact direction first
        if direction.value in enemy_sprites:
            return enemy_sprites[direction.value]

        # Fall back to any available direction
        if enemy_sprites:
            return list(enemy_sprites.values())[0]

        return None

    def get_projectile_sprite(self, projectile_type: str) -> Optional[pygame.Surface]:
        """Get a projectile sprite"""
        if not self.sprites_loaded:
            self.load_all_sprites()

        return self.projectile_sprites.get(projectile_type)

    def _angle_to_direction(self, angle: float) -> Direction:
        """Convert movement angle to the closest direction"""
        # Normalize angle to 0-360 degrees
        angle_degrees = math.degrees(angle) % 360

        # Map angles to directions (0 degrees = East)
        if angle_degrees < 22.5 or angle_degrees >= 337.5:
            return Direction.EAST
        elif angle_degrees < 67.5:
            return Direction.NORTHEAST
        elif angle_degrees < 112.5:
            return Direction.NORTH
        elif angle_degrees < 157.5:
            return Direction.NORTHWEST
        elif angle_degrees < 202.5:
            return Direction.WEST
        elif angle_degrees < 247.5:
            return Direction.SOUTHWEST
        elif angle_degrees < 292.5:
            return Direction.SOUTH
        else:
            return Direction.SOUTHEAST

    def has_tower_sprites(self) -> bool:
        """Check if tower sprites are available"""
        return len(self.tower_sprites) > 0

    def has_enemy_sprites(self) -> bool:
        """Check if enemy sprites are available"""
        return len(self.enemy_sprites) > 0

    def clear_rotation_cache(self):
        """Clear the rotation cache to free memory"""
        self.rotated_tower_cache.clear()

    def get_sprite_info(self) -> Dict:
        """Get information about loaded sprites"""
        return {
            'towers_loaded': len(self.tower_sprites),
            'enemies_loaded': len(self.enemy_sprites),
            'projectiles_loaded': len(self.projectile_sprites),
            'rotation_cache_size': len(self.rotated_tower_cache),
            'tower_sprite_sizes': self.tower_sprite_sizes,
            'enemy_sprite_size': self.enemy_sprite_size
        }
