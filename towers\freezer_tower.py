import math
from typing import List
from .tower import Tower


class FreezerTower(Tower):
    """Tower that completely freezes a single targeted enemy"""

    def __init__(self, x: int, y: int):
        super().__init__(x, y, 'freezer')
        self.range = 85
        self.damage = 7  # Slightly increased damage
        self.fire_rate = 48  # Slightly slower for balance
        self.projectile_speed = 6
        self.size = 13
        self.color = (100, 200, 255)  # Light blue
        # Longer freeze duration (3 seconds) for complete freeze
        self.freeze_duration = 180

        # Complete freeze - stops movement entirely
        self.slow_factor = 0.0  # Complete freeze (0% speed)
        self.complete_freeze = True  # Special flag for complete movement stop

        # Targeting restrictions - ground only
        self.can_target_flying = False
        self.can_target_invisible = False

        # Targeting capabilities - ground only
        self.can_target_flying = False
        self.can_target_invisible = False
        self.can_target_ground = True

        # Finalize initialization to update base stats
        self.finalize_initialization()

    def acquire_target(self, enemies: List):
        """Freezer targets single enemies for complete freeze effect"""
        valid_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))

        if not valid_targets:
            self.target = None
            return

        # Target enemy closest to end of path (priority target)
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def shoot(self, projectiles: List):
        """Fire a freeze projectile that completely freezes a single enemy"""
        if self.target:
            from projectiles import FreezeProjectile
            projectile = FreezeProjectile(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.tower_type, self.freeze_duration, self.complete_freeze
            )
            # Link projectile to tower for damage tracking
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)

            # Generate currency immediately when firing
            self.generate_firing_currency()

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []

        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                if len(valid_targets) >= 10:
                    break

        if not valid_targets:
            self.target = None
            return

        # Target closest to end of path (default strategy)
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier

    def apply_upgrade_effects(self, upgrade_type, level):
        """Ensure complete_freeze is always True after upgrades"""
        super().apply_upgrade_effects(upgrade_type, level)
        self.complete_freeze = True
