from .tower import Tower
import pygame
import math


class SplashTower(Tower):
    """Water-based tower that applies wet status - can only be placed in water"""

    def __init__(self, x, y):
        super().__init__(x, y, 'splash')
        self.damage = 0  # No direct damage
        self.range = 80
        self.fire_rate = 30  # Moderate firing rate
        self.projectile_speed = 6
        self.size = 14
        self.color = (30, 144, 255)  # Deep blue

        # Splash properties
        self.wet_duration = 120  # 2 seconds at 60 FPS
        self.splash_radius = 35  # Area of effect
        self.lightning_damage_multiplier = 2.0  # 2x lightning damage when wet

        # Targeting capabilities - can target flying enemies but not invisible
        self.can_target_flying = True
        self.can_target_invisible = False
        self.can_target_ground = True

        # Placement restriction
        self.water_only = True

        # CRITICAL: Finalize initialization to set base stats correctly
        self.finalize_initialization()

    def can_target_enemy(self, enemy):
        """Check if this tower can target a specific enemy - enhanced for spectral enemies"""
        # Special case: allow targeting detected spectral enemies (even if immune)
        enemy_class_name = type(enemy).__name__
        if enemy_class_name == 'SpectralEnemy':
            if hasattr(enemy, 'detected_by_detector') and enemy.detected_by_detector:
                return True

        # Use base class targeting logic for all other cases
        return super().can_target_enemy(enemy)

    def acquire_target(self, enemies):
        """Find target using targeting restrictions"""
        valid_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))

        if not valid_targets:
            self.target = None
            return

        # Target enemy closest to end of path
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def shoot(self, projectiles):
        """Create water projectile that applies wet status"""
        if self.target:
            from projectiles import WaterProjectile
            projectile = WaterProjectile(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.tower_type, self.wet_duration,
                self.splash_radius, self.lightning_damage_multiplier
            )
            # Link projectile to tower for damage tracking
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)

            # Generate currency immediately when firing
            self.generate_firing_currency()

    def draw(self, screen, selected: bool = False):
        """Draw splash tower with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)

        if selected:
            pygame.draw.circle(screen, (200, 200, 200),
                               (int(self.x), int(self.y)), int(self.range), 1)

        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(
                self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)

                # Draw upgrade indicator if available
                self.draw_upgrade_indicator(screen)
                return

        # Fallback to custom drawing
        # Draw water ripples
        ripple_radius = int(18 + 3 * math.sin(pygame.time.get_ticks() * 0.01))
        pygame.draw.circle(screen, (100, 200, 255),
                           (int(self.x), int(self.y)), ripple_radius, 2)

        # Draw main tower
        pygame.draw.circle(screen, self.color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (0, 0, 0),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw water spout effect
        spout_height = 8
        for i in range(3):
            spout_y = self.y - spout_height - i * 2
            spout_size = 3 - i
            pygame.draw.circle(screen, (173, 216, 230),
                               (int(self.x), int(spout_y)), spout_size)

        # Draw water drops around tower
        for angle in [0, 72, 144, 216, 288]:
            rad = math.radians(angle + pygame.time.get_ticks() * 0.1)
            drop_x = self.x + math.cos(rad) * 10
            drop_y = self.y + math.sin(rad) * 10
            pygame.draw.circle(screen, (135, 206, 235),
                               (int(drop_x), int(drop_y)), 2)

        # Draw upgrade indicator if available
        self.draw_upgrade_indicator(screen)

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []

        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                if len(valid_targets) >= 10:
                    break

        if not valid_targets:
            self.target = None
            return

        # Target closest to end of path (default strategy)
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier
