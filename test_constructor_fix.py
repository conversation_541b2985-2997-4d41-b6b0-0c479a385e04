#!/usr/bin/env python3
"""
Simple test to verify the constructor fix without OpenAI dependency.
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_constructor_without_openai():
    """Test that the constructor works with the new signature"""
    print("🔧 Testing AdaptiveConfigGenerator constructor fix...")
    
    try:
        # Mock the openai import to avoid the dependency
        import sys
        from unittest.mock import MagicMock
        
        # Mock the openai module
        mock_openai = MagicMock()
        sys.modules['openai'] = mock_openai
        
        # Now try to import and create the generator
        from ai.adaptive_config_generator import AdaptiveConfigGenerator
        
        # Test the new constructor signature (should work)
        print("✅ Testing new constructor: AdaptiveConfigGenerator(use_modular_ai=True)")
        generator = AdaptiveConfigGenerator(use_modular_ai=True)
        print("✅ New constructor works!")
        
        # Test that old constructor signature would fail
        print("⚠️ Testing old constructor would fail: AdaptiveConfigGenerator(use_full_ai=False)")
        try:
            generator_old = AdaptiveConfigGenerator(use_full_ai=False)
            print("❌ Old constructor still works - this shouldn't happen!")
            return False
        except TypeError as e:
            if "unexpected keyword argument 'use_full_ai'" in str(e):
                print("✅ Old constructor properly fails with expected error!")
                return True
            else:
                print(f"❌ Old constructor failed with unexpected error: {e}")
                return False
        
    except Exception as e:
        print(f"❌ Constructor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 CONSTRUCTOR FIX VERIFICATION")
    print("="*50)
    
    success = test_constructor_without_openai()
    
    print("\n" + "="*50)
    if success:
        print("✅ CONSTRUCTOR FIX VERIFIED!")
        print("🎉 The game should now work without the 'use_full_ai' error")
    else:
        print("❌ CONSTRUCTOR FIX FAILED!")
        print("🔧 Additional fixes may be needed")

if __name__ == "__main__":
    main()
