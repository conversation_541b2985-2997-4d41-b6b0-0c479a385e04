#!/usr/bin/env python3
"""
Debug script to capture and print all AI inputs and outputs during level generation.
This script will run the AI level generation system and capture all the prompts sent to AI
and all the responses received from AI.
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_modular_ai_generation():
    """Test the modular AI generation system and capture all inputs/outputs"""
    print("\n" + "="*100)
    print("🧩 TESTING MODULAR AI GENERATION SYSTEM")
    print("="*100)

    try:
        from ai.modular_ai_generator import ModularAIGenerator

        # Create the generator
        generator = ModularAIGenerator()

        if not generator.ai_available:
            print("❌ AI is not available. Please check your Azure OpenAI configuration.")
            return

        # Sample performance data for testing
        performance_data = {
            'score': 45.0,
            'win_flag': False,
            'lives_remaining': 3,
            'starting_lives': 20,
            'towers_built': {
                'basic': 12,
                'cannon': 1
            },
            'tower_diversity': 2,
            'previous_difficulty': 40,
            'resources_spent': 600,
            'resources_earned': 800,
            'final_wave': 18,
            'timestamp': datetime.now().isoformat()
        }

        print("📊 PERFORMANCE DATA INPUT:")
        print(json.dumps(performance_data, indent=2))
        print("\n")

        # Generate configuration with modular AI
        print("🧩 Starting Modular AI Generation...")
        config = generator.generate_config_with_ai_modules(
            performance_data=performance_data,
            difficulty=50,
            width=20,
            height=15,
            total_waves=80
        )

        if config:
            print("\n" + "="*80)
            print("✅ FINAL GENERATED CONFIGURATION:")
            print("="*80)
            print(json.dumps(config, indent=2))
            print("="*80)
        else:
            print("❌ Failed to generate configuration")

    except Exception as e:
        print(f"❌ Error in modular AI generation test: {e}")
        import traceback
        traceback.print_exc()


def test_adaptive_generation():
    """Test the adaptive config generator"""
    print("\n" + "="*100)
    print("🎯 TESTING ADAPTIVE CONFIG GENERATION SYSTEM")
    print("="*100)

    try:
        from ai.adaptive_config_generator import AdaptiveConfigGenerator

        # Create the generator with only modular AI
        generator = AdaptiveConfigGenerator(use_modular_ai=True)

        # Sample performance data for testing
        performance_data = {
            'score': 75.0,
            'win_flag': True,
            'lives_remaining': 15,
            'starting_lives': 20,
            'towers_built': {
                'basic': 6,
                'sniper': 8,
                'cannon': 4,
                'ice': 2,
                'splash': 3
            },
            'tower_diversity': 5,
            'previous_difficulty': 60,
            'resources_spent': 1200,
            'resources_earned': 1500,
            'final_wave': 35,
            'timestamp': datetime.now().isoformat()
        }

        print("📊 PERFORMANCE DATA INPUT:")
        print(json.dumps(performance_data, indent=2))
        print("\n")

        # Generate adaptive configuration
        print("🎯 Starting Adaptive Generation...")
        config = generator.generate_adaptive_config(performance_data)

        if config:
            print("\n" + "="*80)
            print("✅ FINAL GENERATED CONFIGURATION:")
            print("="*80)
            print(json.dumps(config, indent=2))
            print("="*80)
        else:
            print("❌ Failed to generate configuration")

    except Exception as e:
        print(f"❌ Error in adaptive generation test: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function to run all AI generation tests"""
    print("🔍 AI LEVEL GENERATION DEBUG SCRIPT")
    print("This script will capture and display all AI inputs and outputs during level generation.")
    print("="*100)

    # Test modular AI generation systems only
    test_modular_ai_generation()
    test_adaptive_generation()

    print("\n" + "="*100)
    print("🏁 DEBUG SCRIPT COMPLETED")
    print("="*100)
    print("All AI inputs and outputs have been printed above.")
    print("Look for sections marked with:")
    print("  🔍 [SYSTEM] AI INPUT PROMPT - Shows what was sent to AI")
    print("  🤖 [SYSTEM] AI OUTPUT RESPONSE - Shows what AI returned")


if __name__ == "__main__":
    main()
